// 自动化测试脚本 - 模拟用户操作并记录结果
const testResults = {
    logs: [],
    panelStates: [],
    errors: []
};

// 监听所有console输出
const originalLog = console.log;
console.log = function(...args) {
    testResults.logs.push({
        timestamp: new Date().toISOString(),
        message: args.join(' ')
    });
    originalLog.apply(console, args);
};

// 监听错误
window.addEventListener('error', (e) => {
    testResults.errors.push({
        timestamp: new Date().toISOString(),
        error: e.error.message,
        stack: e.error.stack
    });
});

// 执行测试
function runAutomatedTest() {
    console.log('🚀 开始自动化测试');
    
    // 1. 检查初始状态
    const panel = document.getElementById('multiOrderPanel');
    testResults.panelStates.push({
        phase: 'initial',
        classes: panel?.className || 'not found',
        display: panel?.style.display || 'default',
        computed: panel ? getComputedStyle(panel).display : 'not found'
    });
    
    // 2. 设置测试数据
    const testInput = `订单1：张三 12345678901 从机场到酒店 2024-12-20 10:00 2人 1箱
订单2：李四 98765432100 从火车站到商场 2024-12-21 14:30 3人 2箱`;
    
    document.getElementById('orderInput').value = testInput;
    
    // 3. 触发测试
    setTimeout(() => {
        console.log('📝 设置测试输入完成');
        
        // 触发测试
        const result = simulateMultiOrderDetection(testInput);
        
        // 4. 检查最终状态
        setTimeout(() => {
            const finalPanel = document.getElementById('multiOrderPanel');
            testResults.panelStates.push({
                phase: 'final',
                classes: finalPanel?.className || 'not found',
                display: finalPanel?.style.display || 'default',
                computed: finalPanel ? getComputedStyle(finalPanel).display : 'not found',
                hasHiddenClass: finalPanel ? finalPanel.classList.contains('hidden') : null
            });
            
            // 5. 生成测试报告
            generateTestReport();
        }, 2000);
        
    }, 500);
}

function generateTestReport() {
    console.log('📊 生成测试报告...');
    
    const report = {
        timestamp: new Date().toISOString(),
        testResults: testResults,
        summary: {
            totalLogs: testResults.logs.length,
            panelStateChanges: testResults.panelStates.length,
            errors: testResults.errors.length,
            multiOrderDetected: testResults.logs.some(log => log.message.includes('收到多订单检测事件')),
            panelShown: testResults.logs.some(log => log.message.includes('多订单面板显示成功')),
            finalPanelVisible: !testResults.panelStates[testResults.panelStates.length - 1]?.hasHiddenClass
        }
    };
    
    console.log('=== 测试报告 ===');
    console.log('总日志条数:', report.summary.totalLogs);
    console.log('面板状态变化:', report.summary.panelStateChanges);
    console.log('错误数量:', report.summary.errors);
    console.log('多订单检测成功:', report.summary.multiOrderDetected);
    console.log('面板显示成功:', report.summary.panelShown);
    console.log('最终面板可见:', report.summary.finalPanelVisible);
    
    console.log('\n=== 详细日志 ===');
    testResults.logs.forEach((log, index) => {
        console.log(`${index + 1}. ${log.message}`);
    });
    
    console.log('\n=== 面板状态变化 ===');
    testResults.panelStates.forEach((state, index) => {
        console.log(`${index + 1}. ${state.phase}:`, state);
    });
    
    if (testResults.errors.length > 0) {
        console.log('\n=== 错误列表 ===');
        testResults.errors.forEach((error, index) => {
            console.log(`${index + 1}. ${error.error}`);
        });
    }
    
    // 输出测试结论
    if (report.summary.multiOrderDetected && report.summary.panelShown && report.summary.finalPanelVisible) {
        console.log('\n✅ 测试成功：多订单检测和面板显示功能正常工作');
    } else {
        console.log('\n❌ 测试失败：存在以下问题：');
        if (!report.summary.multiOrderDetected) console.log('  - 多订单检测事件未触发');
        if (!report.summary.panelShown) console.log('  - 面板显示功能未正常执行');
        if (!report.summary.finalPanelVisible) console.log('  - 最终面板状态不可见');
    }
    
    return report;
}

// 页面加载完成后自动运行测试
window.addEventListener('load', () => {
    setTimeout(runAutomatedTest, 1000);
});

console.log('🔧 自动化测试脚本已加载');