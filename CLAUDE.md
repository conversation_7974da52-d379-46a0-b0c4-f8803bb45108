# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an OTA (Online Travel Agency) order processing system built for GoMyHire integration. It's a static web application that processes travel booking orders through AI-powered text and image analysis, with multi-language support and comprehensive order management features.

## Development Commands

Since this is a static web application:

- **Build**: `npm run build` (outputs message - no build required)
- **Start**: `npm run start` (static site deployment)
- **Local Development**: Open `index.html` in browser or use a local server
- **Testing**: No formal test framework - manual testing through browser

## Architecture Overview

### Module System
- Uses traditional script tag loading (not ES6 modules)
- All modules are loaded in a specific order in `index.html:477-507`
- Global OTA namespace (`window.OTA`) contains all services
- Factory function pattern for singleton services

### Core Services
- **AppState**: Centralized state management with localStorage persistence
- **APIService**: GoMyHire API integration for order creation and data fetching
- **GeminiService**: Google Gemini AI integration for text/image analysis
- **UIManager**: Centralized UI coordination and form management
- **Logger**: Comprehensive logging with performance monitoring

### Key Features
- **Gemini Vision AI**: Image upload analysis for order information extraction
- **One-Step Multi-Order Processing**: Complete detection, parsing, and field mapping in single AI call
- **Structured Order Display**: Direct field-level editing of parsed orders
- **Ultra-Compact Mobile UI**: Mobile-first responsive design with extreme space optimization (v2.0)
- **Multi-Select Language Support**: Advanced tickbox dropdown for language selection
- **Currency Conversion**: Auto-converts between MYR/USD/SGD/CNY
- **Internationalization**: Full Chinese/English support
- **Grid Layout**: Resizable panel system for form organization
- **Order History**: Local storage with search and export capabilities

### Important File Dependencies

Core loading order (from index.html):
1. `js/utils.js` - Utility functions
2. `js/logger.js` - Logging system
3. `js/monitoring-wrapper.js` - Performance monitoring
4. `js/ota-channel-mapping.js` - OTA channel mappings
5. `js/app-state.js` - State management
6. `js/api-service.js` - API integration
7. `js/gemini-service.js` - AI services
8. Manager modules (form, price, event, state, realtime-analysis)
9. `js/ui-manager.js` - UI coordination
10. `main.js` - Application bootstrap

### API Integration
- **Base URL**: https://gomyhire.com.my/api
- **Authentication**: JWT token-based
- **Key Endpoints**: 
  - `/login` - User authentication
  - `/orders` - Order CRUD operations
  - `/system-data` - Configuration data (car types, regions, etc.)

### Development Guidelines

#### Code Style
- Uses Chinese comments for complex business logic
- Factory functions for service instantiation
- Traditional script loading (no ES6 modules)
- Comprehensive error handling and logging

#### Key Components
- **Form Management**: Centralized through FormManager
- **State Management**: Reactive state with change listeners
- **Error Handling**: Global error capture with user-friendly messages
- **Performance Monitoring**: Built-in performance tracking

#### Configuration
- Static data embedded in api-service.js for offline functionality
- Environment configuration through meta tags or URL parameters
- Cursor rules enforce structured development workflow (Chinese language, RIPER-5 framework)

### Memory Bank System
The project includes a memory-bank folder for context persistence between sessions, though current files are empty. This system is designed to maintain development context across different coding sessions.

### Notable Patterns
- **Singleton Services**: All major services use factory functions
- **Observer Pattern**: State changes trigger UI updates
- **Strategy Pattern**: Different AI analysis strategies for text vs images
- **Chain of Responsibility**: Multi-step order processing pipeline

## Multi-Order Processing (Enhanced v2.0) with Ultra-Compact Mobile UI

### Overview
The multi-order system has been completely redesigned to implement a **one-step AI processing workflow** with revolutionary **ultra-compact mobile interface** that maximizes space efficiency while maintaining full functionality.

### Enhanced Workflow
```
User Input → Gemini One-Step Analysis → Ultra-Compact Mobile Display → Field Editing → Batch Creation
```

### Mobile UI Revolution (v2.0)
1. **Extreme Space Optimization**: Button heights reduced from 44px to 18-24px (55% space saving)
2. **Ultra-Compact Spacing**: Reduced from 16px+ to 1-2px (85% space saving)  
3. **Advanced Language Selection**: Multi-select tickbox dropdown with 4+ language options
4. **Super-Compact Footer**: Bottom navigation compressed to minimal viable size
5. **Four-Column Layout**: Order summary upgraded from 3-column to 4-column for maximum information density
6. **Responsive Breakpoints**: Optimized for 768px → 480px progressive compaction

### Key Improvements (v2.0)
1. **One-Step AI Processing**: Single Gemini call for detection + parsing + field mapping
2. **Structured Data Display**: Direct visualization of parsed order fields
3. **Real-time Field Editing**: Live editing with instant summary updates
4. **Enhanced UI/UX**: Order cards with summary/detail views

### Technical Implementation

#### Gemini Service Enhancement
- **File**: `js/gemini-service.js`
- **Method**: `detectAndSplitMultiOrders(orderText)`
- **Returns**: Structured order objects with complete field mapping
- **Features**: Built-in field validation, smart ID mapping, currency recognition

#### Multi-Order Manager Redesign
- **File**: `js/multi-order-manager.js`
- **Key Methods**:
  - `showMultiOrderPanel(orders)` - Displays parsed order objects
  - `generateOrderSummary(order)` - Creates order summary cards
  - `generateOrderFieldsHTML(order, index)` - Builds editable field forms
  - `updateOrderField(index, fieldName, value)` - Real-time field updates

#### UI Components
- **Order Summary Cards**: Customer, route, time, passengers, price
- **Detailed Field Editor**: All order fields with validation
- **Toggle Views**: Switch between summary and detail modes
- **Status Indicators**: Parsed, processing, validation states

### Data Structure
```javascript
{
  isMultiOrder: boolean,
  orderCount: number,
  confidence: number,
  analysis: string,
  orders: [
    {
      rawText: string,
      customerName: string,
      customerContact: string,
      customerEmail: string,
      pickup: string,
      dropoff: string,
      pickupDate: "YYYY-MM-DD",
      pickupTime: "HH:MM",
      passengerCount: number,
      luggageCount: number,
      flightInfo: string,
      otaReferenceNumber: string,
      otaPrice: number,
      currency: "MYR|USD|SGD|CNY",
      carTypeId: number,
      subCategoryId: number,
      drivingRegionId: number,
      languagesIdArray: [number],
      extraRequirement: string,
      babyChair: boolean,
      tourGuide: boolean,
      meetAndGreet: boolean
    }
  ]
}
```

### AI Prompt Strategy
- **Complete field mapping** in single request
- **Smart ID resolution** based on business rules
- **Field validation** and data cleaning
- **Fallback mechanisms** for parsing failures

### User Experience
1. **Input**: User pastes multi-order text
2. **Processing**: Gemini analyzes and returns structured data
3. **Display**: Orders shown as editable cards with summaries
4. **Editing**: Click "详情" to expand full field editor
5. **Validation**: Real-time field validation and suggestions
6. **Creation**: Batch creation of validated orders

## Global Functions and References Structure

### Core Namespace Architecture
```javascript
window.OTA = {
    // Core services
    app: OTAApplication,              // Main application instance
    appState: AppState,               // Application state management
    apiService: ApiService,           // API service
    geminiService: GeminiService,     // AI service
    uiManager: UIManager,             // UI manager
    logger: Logger,                   // Logging service
    utils: Utils,                     // Utility functions
    
    // Business modules
    multiOrderManager: MultiOrderManager,
    orderHistoryManager: OrderHistoryManager,
    imageUploadManager: ImageUploadManager,
    currencyConverter: CurrencyConverter,
    
    // Sub-namespaces
    managers: {
        EventManager, FormManager, StateManager, 
        PriceManager, RealtimeAnalysisManager
    }
}
```

### Factory Function Pattern
All services use factory functions for singleton management:
```javascript
// Core service factories
window.getAppState = getAppState;                    // app-state.js
window.getAPIService = getAPIService;                // api-service.js  
window.getGeminiService = getGeminiService;          // gemini-service.js
window.getMultiOrderManager = getMultiOrderManager; // multi-order-manager.js
window.getOrderHistoryManager = getOrderHistoryManager; // order-history-manager.js
window.getImageUploadManager = getImageUploadManager; // image-upload-manager.js
window.getCurrencyConverter = getCurrencyConverter; // currency-converter.js
window.getI18nManager = getI18nManager;             // i18n.js
```

### Global Utility Functions
```javascript
window.OTA.utils = {
    debounce, throttle, deepClone, formatDate, formatTime,
    generateId, isValidEmail, isValidPhone, parseDate,
    isMobile, getBrowserInfo, downloadFile, parseUrlParams,
    safeJsonParse, objectDiff, performanceMonitor
};
```

### Initialization Flow
1. **DOMContentLoaded** → Creates OTAApplication instance
2. **app.init()** → Initializes all core services
3. **Module initialization** → Each service's init() method
4. **Health check** → performSystemHealthCheck()
5. **Monitoring setup** → setupMonitoringCommands()

### Module Dependencies
```
main.js (entry point)
├── utils.js (no dependencies)
├── logger.js (depends on utils)
├── app-state.js (depends on utils, logger)
├── api-service.js (depends on app-state, logger)
├── gemini-service.js (depends on app-state, logger)
├── ui-manager.js (depends on all managers)
└── business modules (depend on core services)
```

### Event System
- **System events**: error, unhandledrejection, beforeunload, online/offline
- **Business events**: multiOrderDetected, orderCreated, orderValidated
- **UI events**: input, paste, change, submit

### Performance Monitoring
Built-in monitoring system with global commands:
```javascript
window.monitoring = {
    report(),                // Show monitoring report
    setRealTime(enabled),    // Enable/disable real-time monitoring
    setDebug(enabled),       // Enable/disable debug mode
    clear(),                 // Clear monitoring data
    export(format)           // Export monitoring data
};
```

### Backward Compatibility
Each module uses dual declaration strategy:
```javascript
// New namespace form
window.OTA.moduleName = module;
// Traditional global form (backward compatible)
window.moduleName = module;
```

## Deployment

- **Platform**: Netlify
- **Site ID**: 578f091d-52e6-405d-9001-b2f0801e4cbf
- **Domain**: gmhcreateorder.netlify.app
- **Configuration**: netlify.toml for build settings