/**
 * 手动测试脚本
 * 可以在浏览器控制台中直接运行来测试多订单检测功能
 */

// 测试文本
const testText = `[2025/7/15 15:42] Joshua: 送机：

团号：EJBTBY250717
2PAX
21/7 MOXY PUTRAJAYA PICKUP 0600 - KLIA2 (AK188 1000)
客人：简锦霞
客人联系： ***********
[2025/7/15 18:48] Joshua: 接机：

团号：EJBTBY250716-7
2PAX
16/7 KLIA IN 1840 (MH377) - KOMUNE LIVING PERMAISURI
客人：周有改
客人联系：***********
[2025/7/15 18:48] Joshua: 7 SEATER

接机：

团号：EJBTBY250717-1
4PAX
17/7 KLIA IN 1325 - DAYS HOTEL FRASER BUSINESS PARK
客人：谭建玲
客人联系： ***********
[2025/7/15 18:48] Joshua: 7 SEATER

送机：

团号：EJBTBY250717-1
4PAX
21/7 MOXY PUTRAJAYA PICKUP 0400 - KLIA2 (AK5106 0800)
客人：谭建玲
客人联系： ***********
[2025/7/15 21:53] Joshua: 送机：

团号：EJBTBY250712-1
2PAX
16/7 CONCORDE SHAH ALAM PICKUP 0530 - KLIA2 (AK707 0935)
客人：朱芸 
客人联系：***********
[2025/7/15 23:00] Joshua: 酒店接送：

团号：EJBTBY250714-1
2PAX
16/7 Crown Regency Serviced Suites PICKUP 1200 - Hotel Komune Living & Wellness (PERMAISURI)
客人：吴敏 
客人联系：***********`;

/**
 * 在控制台中运行此函数来测试多订单检测
 */
async function testMultiOrderDetection() {
    console.log('🚀 开始手动测试多订单检测功能');
    console.log(`📝 测试文本长度: ${testText.length} 字符`);
    console.log('-------------------------------------------');
    
    try {
        // 检查服务是否可用
        const geminiService = window.OTA?.geminiService || window.getGeminiService?.();
        if (!geminiService) {
            console.error('❌ GeminiService 不可用');
            return;
        }
        
        console.log('✅ GeminiService 可用，开始检测...');
        
        const startTime = Date.now();
        const result = await geminiService.detectAndSplitMultiOrders(testText);
        const endTime = Date.now();
        
        console.log('✅ 检测完成');
        console.log(`⏱️ 响应时间: ${endTime - startTime}ms`);
        console.log('-------------------------------------------');
        console.log('📊 检测结果:');
        console.log(`是否多订单: ${result.isMultiOrder}`);
        console.log(`订单数量: ${result.orderCount}`);
        console.log(`置信度: ${(result.confidence * 100).toFixed(1)}%`);
        console.log(`分析说明: ${result.analysis}`);
        console.log('-------------------------------------------');
        
        if (result.orders && result.orders.length > 0) {
            console.log('📋 订单详情:');
            result.orders.forEach((order, index) => {
                console.log(`订单 ${index + 1}:`);
                console.log(`  客户: ${order.customerName}`);
                console.log(`  联系: ${order.customerContact}`);
                console.log(`  路线: ${order.pickup} → ${order.dropoff}`);
                console.log(`  时间: ${order.pickupDate} ${order.pickupTime}`);
                console.log(`  团号: ${order.otaReferenceNumber}`);
                console.log(`  类型: ${order.subCategoryId} (${getServiceTypeName(order.subCategoryId)})`);
            });
        }
        
        console.log('-------------------------------------------');
        console.log('🎯 结果验证:');
        console.log(`预期订单数: 6, 实际检测: ${result.orderCount} ${result.orderCount === 6 ? '✅' : '❌'}`);
        console.log(`预期多订单: true, 实际检测: ${result.isMultiOrder} ${result.isMultiOrder ? '✅' : '❌'}`);
        
        // 返回结果供进一步分析
        window.lastDetectionResult = result;
        console.log('💾 结果已保存到 window.lastDetectionResult');
        
        return result;
        
    } catch (error) {
        console.error('❌ 检测失败:', error.message);
        console.error('📋 错误详情:', error);
        return null;
    }
}

/**
 * 获取服务类型名称
 */
function getServiceTypeName(subCategoryId) {
    const types = {
        2: '接机',
        3: '送机', 
        4: '包车/其他'
    };
    return types[subCategoryId] || '未知';
}

/**
 * 测试事件触发
 */
function testEventTrigger() {
    if (!window.lastDetectionResult) {
        console.error('❌ 请先运行 testMultiOrderDetection()');
        return;
    }
    
    console.log('📡 测试事件触发机制...');
    
    // 设置事件监听器
    const eventListener = function(event) {
        console.log('🔔 收到 multiOrderDetected 事件');
        console.log('📦 事件数据:', event.detail);
        document.removeEventListener('multiOrderDetected', eventListener);
    };
    
    document.addEventListener('multiOrderDetected', eventListener);
    
    // 触发事件
    const event = new CustomEvent('multiOrderDetected', {
        detail: {
            multiOrderResult: window.lastDetectionResult,
            orderText: testText
        }
    });
    
    document.dispatchEvent(event);
    console.log('✅ 事件已触发');
}

/**
 * 完整测试流程
 */
async function runFullTest() {
    console.clear();
    console.log('🔬 开始完整的多订单检测测试流程');
    console.log('===========================================');
    
    // 步骤1：检测测试
    const result = await testMultiOrderDetection();
    
    if (result) {
        console.log('\n');
        // 步骤2：事件测试
        testEventTrigger();
        
        console.log('\n===========================================');
        console.log('✅ 完整测试流程执行完毕');
        console.log('💡 提示: 检查上述输出，查看是否存在问题');
    } else {
        console.log('\n===========================================');
        console.log('❌ 测试失败，请检查错误信息');
    }
}

// 暴露函数到全局作用域
window.testMultiOrderDetection = testMultiOrderDetection;
window.testEventTrigger = testEventTrigger;
window.runFullTest = runFullTest;

console.log('🔧 手动测试脚本已加载');
console.log('💡 使用方法:');
console.log('   - runFullTest() - 执行完整测试');
console.log('   - testMultiOrderDetection() - 仅测试检测功能');
console.log('   - testEventTrigger() - 仅测试事件触发');