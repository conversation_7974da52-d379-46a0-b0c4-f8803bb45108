<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户操作学习器测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        button { margin: 5px; padding: 8px 16px; }
        .stats { background: #e8f4f8; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>用户操作学习器测试</h1>
    
    <div class="test-section">
        <h2>模块加载测试</h2>
        <div id="moduleTest"></div>
    </div>

    <div class="test-section">
        <h2>操作记录测试</h2>
        <button onclick="testRecordOperation()">测试记录操作</button>
        <button onclick="testBatchOperations()">测试批量操作</button>
        <button onclick="testInvalidOperation()">测试无效操作</button>
        <div id="recordTest"></div>
    </div>

    <div class="test-section">
        <h2>查询功能测试</h2>
        <button onclick="testQueryOperations()">测试查询操作</button>
        <button onclick="testQueryByField()">按字段查询</button>
        <button onclick="testQueryByType()">按类型查询</button>
        <div id="queryTest"></div>
    </div>

    <div class="test-section">
        <h2>统计信息</h2>
        <button onclick="updateStats()">更新统计</button>
        <button onclick="resetStats()">重置统计</button>
        <div id="statsDisplay" class="stats"></div>
    </div>

    <div class="test-section">
        <h2>数据清理测试</h2>
        <button onclick="testDataCleanup()">测试数据清理</button>
        <div id="cleanupTest"></div>
    </div>

    <!-- 加载必要的模块 -->
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/learning-engine/learning-config.js"></script>
    <script src="js/learning-engine/learning-storage-manager.js"></script>
    <script src="js/learning-engine/user-operation-learner.js"></script>

    <script>
        let learner = null;

        // 初始化测试
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(initializeTests, 100);
        });

        function initializeTests() {
            const moduleDiv = document.getElementById('moduleTest');
            
            try {
                // 测试模块加载
                if (window.OTA && window.OTA.userOperationLearner) {
                    learner = getUserOperationLearner();
                    moduleDiv.innerHTML += '<div class="success">✅ 用户操作学习器加载成功</div>';
                    moduleDiv.innerHTML += `<div class="info">版本: ${learner.version}</div>`;
                    moduleDiv.innerHTML += `<div class="info">会话ID: ${learner.sessionId}</div>`;
                } else {
                    moduleDiv.innerHTML += '<div class="error">❌ 用户操作学习器加载失败</div>';
                    return;
                }

                // 初始统计
                updateStats();

            } catch (error) {
                moduleDiv.innerHTML += `<div class="error">初始化错误: ${error.message}</div>`;
                console.error('初始化错误:', error);
            }
        }

        function testRecordOperation() {
            const resultDiv = document.getElementById('recordTest');
            
            try {
                const operationData = {
                    type: 'correction',
                    field: 'customerName',
                    originalValue: 'John Doe',
                    correctedValue: 'John Smith',
                    context: {
                        orderText: '测试订单文本',
                        aiAnalysisResult: { confidence: 0.8 }
                    },
                    confidence: 0.9,
                    metadata: {
                        source: 'manual_test',
                        errorType: 'name_extraction_error'
                    }
                };

                const operationId = learner.recordOperation(operationData);
                
                if (operationId) {
                    resultDiv.innerHTML += `<div class="success">✅ 操作记录成功，ID: ${operationId}</div>`;
                } else {
                    resultDiv.innerHTML += '<div class="error">❌ 操作记录失败</div>';
                }

                updateStats();

            } catch (error) {
                resultDiv.innerHTML += `<div class="error">测试错误: ${error.message}</div>`;
            }
        }

        function testBatchOperations() {
            const resultDiv = document.getElementById('recordTest');
            
            try {
                const operations = [
                    {
                        type: 'correction',
                        field: 'pickup',
                        originalValue: 'KLIA',
                        correctedValue: 'Kuala Lumpur International Airport',
                        confidence: 0.85
                    },
                    {
                        type: 'validation',
                        field: 'pickupTime',
                        originalValue: '10:30',
                        correctedValue: '10:30',
                        confidence: 1.0
                    },
                    {
                        type: 'feedback',
                        field: 'passengerCount',
                        originalValue: '2',
                        correctedValue: '3',
                        confidence: 0.7
                    }
                ];

                let successCount = 0;
                operations.forEach((op, index) => {
                    const id = learner.recordOperation(op);
                    if (id) successCount++;
                });

                resultDiv.innerHTML += `<div class="success">✅ 批量操作完成，成功记录 ${successCount}/${operations.length} 个操作</div>`;
                updateStats();

            } catch (error) {
                resultDiv.innerHTML += `<div class="error">批量测试错误: ${error.message}</div>`;
            }
        }

        function testInvalidOperation() {
            const resultDiv = document.getElementById('recordTest');
            
            try {
                // 测试无效操作
                const invalidOperations = [
                    { type: 'invalid_type', field: 'test' }, // 无效类型
                    { type: 'correction' }, // 缺少必需字段
                    { type: 'correction', field: '', originalValue: 'test', correctedValue: 'test' } // 空字段名
                ];

                let rejectedCount = 0;
                invalidOperations.forEach(op => {
                    const id = learner.recordOperation(op);
                    if (!id) rejectedCount++;
                });

                resultDiv.innerHTML += `<div class="success">✅ 无效操作测试完成，正确拒绝 ${rejectedCount}/${invalidOperations.length} 个无效操作</div>`;

            } catch (error) {
                resultDiv.innerHTML += `<div class="error">无效操作测试错误: ${error.message}</div>`;
            }
        }

        function testQueryOperations() {
            const resultDiv = document.getElementById('queryTest');
            
            try {
                const allOperations = learner.queryOperations();
                resultDiv.innerHTML += `<div class="info">查询到 ${allOperations.length} 个操作记录</div>`;
                
                if (allOperations.length > 0) {
                    resultDiv.innerHTML += '<div class="success">✅ 查询功能正常</div>';
                    resultDiv.innerHTML += `<pre>${JSON.stringify(allOperations.slice(0, 2), null, 2)}</pre>`;
                } else {
                    resultDiv.innerHTML += '<div class="warning">⚠️ 没有找到操作记录</div>';
                }

            } catch (error) {
                resultDiv.innerHTML += `<div class="error">查询测试错误: ${error.message}</div>`;
            }
        }

        function testQueryByField() {
            const resultDiv = document.getElementById('queryTest');
            
            try {
                const fieldOperations = learner.queryOperations({ field: 'customerName' });
                resultDiv.innerHTML += `<div class="info">customerName字段操作: ${fieldOperations.length} 个</div>`;
                
                const pickupOperations = learner.queryOperations({ field: 'pickup' });
                resultDiv.innerHTML += `<div class="info">pickup字段操作: ${pickupOperations.length} 个</div>`;

            } catch (error) {
                resultDiv.innerHTML += `<div class="error">按字段查询错误: ${error.message}</div>`;
            }
        }

        function testQueryByType() {
            const resultDiv = document.getElementById('queryTest');
            
            try {
                const corrections = learner.queryOperations({ type: 'correction' });
                resultDiv.innerHTML += `<div class="info">correction类型操作: ${corrections.length} 个</div>`;
                
                const validations = learner.queryOperations({ type: 'validation' });
                resultDiv.innerHTML += `<div class="info">validation类型操作: ${validations.length} 个</div>`;

            } catch (error) {
                resultDiv.innerHTML += `<div class="error">按类型查询错误: ${error.message}</div>`;
            }
        }

        function updateStats() {
            const statsDiv = document.getElementById('statsDisplay');
            
            try {
                const stats = learner.getStats();
                statsDiv.innerHTML = `
                    <h3>统计信息</h3>
                    <p><strong>总操作数:</strong> ${stats.totalOperations}</p>
                    <p><strong>会话操作数:</strong> ${stats.sessionOperations}</p>
                    <p><strong>最后操作时间:</strong> ${stats.lastOperation || '无'}</p>
                `;

            } catch (error) {
                statsDiv.innerHTML = `<div class="error">获取统计信息错误: ${error.message}</div>`;
            }
        }

        function resetStats() {
            try {
                learner.resetStats();
                updateStats();
                document.getElementById('statsDisplay').innerHTML += '<div class="success">✅ 统计信息已重置</div>';
            } catch (error) {
                document.getElementById('statsDisplay').innerHTML += `<div class="error">重置统计错误: ${error.message}</div>`;
            }
        }

        function testDataCleanup() {
            const resultDiv = document.getElementById('cleanupTest');
            
            try {
                // 手动触发清理（通常由定时器自动执行）
                learner.cleanupExpiredData();
                resultDiv.innerHTML += '<div class="success">✅ 数据清理测试完成</div>';
                updateStats();

            } catch (error) {
                resultDiv.innerHTML += `<div class="error">数据清理测试错误: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
