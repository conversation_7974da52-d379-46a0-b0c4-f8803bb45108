/**
 * 测试调试工具的简单脚本
 * 模拟浏览器环境来测试多订单检测
 */

const fs = require('fs');
const path = require('path');

// 模拟浏览器DOM环境
global.window = {
    OTA: {},
    addEventListener: () => {},
    CustomEvent: class CustomEvent {
        constructor(type, init) {
            this.type = type;
            this.detail = init ? init.detail : {};
        }
    },
    document: {
        addEventListener: () => {},
        dispatchEvent: () => {}
    }
};

global.document = global.window.document;

// 模拟console
global.console = console;

// 读取测试文本
const testText = `[2025/7/15 15:42] Joshua: 送机：

团号：EJBTBY250717
2PAX
21/7 MOXY PUTRAJAYA PICKUP 0600 - KLIA2 (AK188 1000)
客人：简锦霞
客人联系： ***********
[2025/7/15 18:48] Joshua: 接机：

团号：EJBTBY250716-7
2PAX
16/7 KLIA IN 1840 (MH377) - KOMUNE LIVING PERMAISURI
客人：周有改
客人联系：***********
[2025/7/15 18:48] Joshua: 7 SEATER

接机：

团号：EJBTBY250717-1
4PAX
17/7 KLIA IN 1325 - DAYS HOTEL FRASER BUSINESS PARK
客人：谭建玲
客人联系： ***********
[2025/7/15 18:48] Joshua: 7 SEATER

送机：

团号：EJBTBY250717-1
4PAX
21/7 MOXY PUTRAJAYA PICKUP 0400 - KLIA2 (AK5106 0800)
客人：谭建玲
客人联系： ***********
[2025/7/15 21:53] Joshua: 送机：

团号：EJBTBY250712-1
2PAX
16/7 CONCORDE SHAH ALAM PICKUP 0530 - KLIA2 (AK707 0935)
客人：朱芸 
客人联系：***********
[2025/7/15 23:00] Joshua: 酒店接送：

团号：EJBTBY250714-1
2PAX
16/7 Crown Regency Serviced Suites PICKUP 1200 - Hotel Komune Living & Wellness (PERMAISURI)
客人：吴敏 
客人联系：***********`;

console.log('测试文本分析：');
console.log(`- 文本长度: ${testText.length} 字符`);
console.log(`- 包含"团号"次数: ${(testText.match(/团号/g) || []).length}`);
console.log(`- 包含"客人"次数: ${(testText.match(/客人/g) || []).length}`);
console.log(`- 包含"Joshua"次数: ${(testText.match(/Joshua/g) || []).length}`);
console.log(`- 包含日期模式次数: ${(testText.match(/\d{1,2}\/\d{1,2}/g) || []).length}`);

// 简单的文本分析
const lines = testText.split('\n').filter(line => line.trim());
console.log(`- 总行数: ${lines.length}`);

const orderSections = testText.split('Joshua:').filter(section => section.trim());
console.log(`- 按Joshua分割的部分: ${orderSections.length}`);

orderSections.forEach((section, index) => {
    console.log(`\n部分 ${index + 1}:`);
    console.log(`  长度: ${section.trim().length} 字符`);
    console.log(`  包含团号: ${section.includes('团号')}`);
    console.log(`  包含客人: ${section.includes('客人')}`);
});

console.log('\n预期结果：应该检测到6个独立的订单');