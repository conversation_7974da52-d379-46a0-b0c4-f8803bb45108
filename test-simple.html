<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .button:hover { background: #0056b3; }
        .log { background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px; }
        .result { background: #e7f3ff; border: 1px solid #b8daff; padding: 10px; white-space: pre-wrap; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 多订单检测简单测试</h1>
        
        <div class="section">
            <h3>测试状态</h3>
            <div id="status">准备测试...</div>
            <button class="button" onclick="runTest()">开始测试</button>
            <button class="button" onclick="clearResults()">清空结果</button>
        </div>
        
        <div class="section">
            <h3>测试结果</h3>
            <div id="result" class="result">等待测试结果...</div>
        </div>
        
        <div class="section">
            <h3>执行日志</h3>
            <div id="log" class="log">日志将在此显示...</div>
        </div>
    </div>

    <script>
        // 测试文本
        const testText = `[2025/7/15 15:42] Joshua: 送机：

团号：EJBTBY250717
2PAX
21/7 MOXY PUTRAJAYA PICKUP 0600 - KLIA2 (AK188 1000)
客人：简锦霞
客人联系： ***********
[2025/7/15 18:48] Joshua: 接机：

团号：EJBTBY250716-7
2PAX
16/7 KLIA IN 1840 (MH377) - KOMUNE LIVING PERMAISURI
客人：周有改
客人联系：***********
[2025/7/15 18:48] Joshua: 7 SEATER

接机：

团号：EJBTBY250717-1
4PAX
17/7 KLIA IN 1325 - DAYS HOTEL FRASER BUSINESS PARK
客人：谭建玲
客人联系： ***********
[2025/7/15 18:48] Joshua: 7 SEATER

送机：

团号：EJBTBY250717-1
4PAX
21/7 MOXY PUTRAJAYA PICKUP 0400 - KLIA2 (AK5106 0800)
客人：谭建玲
客人联系： ***********
[2025/7/15 21:53] Joshua: 送机：

团号：EJBTBY250712-1
2PAX
16/7 CONCORDE SHAH ALAM PICKUP 0530 - KLIA2 (AK707 0935)
客人：朱芸 
客人联系：***********
[2025/7/15 23:00] Joshua: 酒店接送：

团号：EJBTBY250714-1
2PAX
16/7 Crown Regency Serviced Suites PICKUP 1200 - Hotel Komune Living & Wellness (PERMAISURI)
客人：吴敏 
客人联系：***********`;

        let logBuffer = [];
        
        function log(message, level = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            logBuffer.push(logEntry);
            
            const logElement = document.getElementById('log');
            if (logElement) {
                logElement.textContent = logBuffer.join('\n');
                logElement.scrollTop = logElement.scrollHeight;
            }
            
            console.log(`[${level.toUpperCase()}] ${logEntry}`);
        }
        
        function updateStatus(message, type = 'info') {
            const statusElement = document.getElementById('status');
            if (statusElement) {
                statusElement.textContent = message;
                statusElement.style.color = type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#007bff';
            }
        }
        
        function updateResult(data) {
            const resultElement = document.getElementById('result');
            if (resultElement) {
                resultElement.textContent = typeof data === 'string' ? data : JSON.stringify(data, null, 2);
            }
        }
        
        async function runTest() {
            try {
                updateStatus('正在初始化测试环境...', 'info');
                log('🚀 开始简单测试', 'info');
                log(`测试文本长度: ${testText.length} 字符`, 'info');
                
                // 检查文本特征
                const features = {
                    orderMarkers: (testText.match(/团号/g) || []).length,
                    customerMarkers: (testText.match(/客人/g) || []).length,
                    joshuaMarkers: (testText.match(/Joshua/g) || []).length,
                    datePatterns: (testText.match(/\d{1,2}\/\d{1,2}/g) || []).length
                };
                
                log(`检测到的特征: ${JSON.stringify(features)}`, 'info');
                
                // 简单的订单分割测试
                const sections = testText.split('Joshua:').filter(s => s.trim());
                const validSections = sections.filter(s => s.includes('团号') && s.includes('客人'));
                
                log(`Joshua分割的有效部分: ${validSections.length}`, 'info');
                
                const mockResult = {
                    isMultiOrder: validSections.length > 1,
                    orderCount: validSections.length,
                    confidence: validSections.length >= 6 ? 0.95 : 0.8,
                    analysis: `检测到${validSections.length}个订单片段，每个包含团号和客人信息`,
                    testData: {
                        textLength: testText.length,
                        features: features,
                        sections: validSections.length
                    }
                };
                
                updateResult(mockResult);
                updateStatus(`模拟测试完成 - 发现${validSections.length}个订单`, 'success');
                log(`✅ 模拟测试完成`, 'success');
                
                // 检查是否有实际的GeminiService
                if (window.getGeminiService) {
                    log('🔍 发现GeminiService，尝试真实调用...', 'info');
                    updateStatus('正在调用GeminiService...', 'info');
                    
                    const geminiService = window.getGeminiService();
                    if (geminiService && geminiService.detectAndSplitMultiOrders) {
                        const realResult = await geminiService.detectAndSplitMultiOrders(testText);
                        updateResult(realResult);
                        updateStatus(`真实测试完成 - ${realResult.isMultiOrder ? '多订单' : '单订单'}`, 'success');
                        log(`✅ 真实测试完成: ${JSON.stringify(realResult, null, 2)}`, 'success');
                    } else {
                        log('❌ GeminiService方法不可用', 'error');
                    }
                } else {
                    log('ℹ️ GeminiService不可用，仅显示模拟结果', 'info');
                }
                
            } catch (error) {
                updateStatus(`测试失败: ${error.message}`, 'error');
                log(`❌ 测试失败: ${error.message}`, 'error');
                updateResult(`错误: ${error.message}\n\n堆栈: ${error.stack}`);
            }
        }
        
        function clearResults() {
            logBuffer = [];
            document.getElementById('log').textContent = '日志已清空...';
            document.getElementById('result').textContent = '等待测试结果...';
            updateStatus('准备测试...', 'info');
            log('🧹 结果已清空', 'info');
        }
        
        // 页面加载完成后的检查
        window.addEventListener('DOMContentLoaded', function() {
            log('📄 页面加载完成', 'info');
            log('🔍 检查依赖...', 'info');
            
            const dependencies = [
                'window.getGeminiService',
                'window.getAppState',
                'window.getLogger',
                'window.OTA'
            ];
            
            dependencies.forEach(dep => {
                const exists = eval(`typeof ${dep}`) !== 'undefined';
                log(`${dep}: ${exists ? '✅ 可用' : '❌ 不可用'}`, exists ? 'info' : 'warn');
            });
        });
    </script>
    
    <!-- 尝试加载主要依赖 -->
    <script src="js/utils.js" onerror="console.log('utils.js加载失败')"></script>
    <script src="js/logger.js" onerror="console.log('logger.js加载失败')"></script>
    <script src="js/app-state.js" onerror="console.log('app-state.js加载失败')"></script>
    <script src="js/gemini-service.js" onerror="console.log('gemini-service.js加载失败')"></script>
</body>
</html>