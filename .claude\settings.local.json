{"permissions": {"allow": ["Bash(ls:*)", "Bash(rm:*)", "Bash(start index.html)", "Bash(cmd.exe /C start test-ota-channels.html)", "Bash(cmd.exe /C start test-batch-settings.html)", "<PERSON><PERSON>(python:*)", "WebFetch(domain:)", "Bash(start \"C:\\Users\\<USER>\\Downloads\\create job\\test-multi-order-detection.html\")", "Bash(cmd /c start \"\" \"C:\\Users\\<USER>\\Downloads\\create job\\test-multi-order-detection.html\")", "Bash(where chrome)", "<PERSON><PERSON>(dir:*)", "Bash(\"C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe\" \"file:///C:/Users/<USER>/Downloads/create%20job/debug-multi-order.html\")", "<PERSON><PERSON>(timeout:*)", "Bash(node:*)", "Bash(\"C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe\" \"file:///C:/Users/<USER>/Downloads/create%20job/test-simple.html\")", "Bash(\"C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe\" --new-window \"file:///C:/Users/<USER>/Downloads/create%20job/debug-multi-order.html\")", "<PERSON><PERSON>(powershell:*)", "Bash(start chrome \"file:///C:/Users/<USER>/Downloads/create%20job/test-gemini-fix.html\")", "Bash(explorer \"C:\\Users\\<USER>\\Downloads\\create job\\test-gemini-fix.html\")", "Bash(grep:*)"], "deny": []}}