<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能学习型格式预处理引擎 - 管理面板</title>
    
    <!-- 引入Chart.js用于数据可视化 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .dashboard-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .dashboard-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .dashboard-header h1 {
            color: #4a5568;
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .dashboard-header p {
            color: #718096;
            font-size: 1.1rem;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .dashboard-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e2e8f0;
        }

        .card-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 1.5rem;
            color: white;
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2d3748;
        }

        .metric-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }

        .metric-item {
            text-align: center;
            padding: 15px;
            background: rgba(247, 250, 252, 0.8);
            border-radius: 12px;
            border: 1px solid rgba(226, 232, 240, 0.5);
        }

        .metric-value {
            font-size: 1.8rem;
            font-weight: 700;
            color: #2b6cb0;
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 0.9rem;
            color: #718096;
            font-weight: 500;
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin-top: 20px;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-active { background-color: #48bb78; }
        .status-warning { background-color: #ed8936; }
        .status-error { background-color: #f56565; }
        .status-inactive { background-color: #a0aec0; }

        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }

        .btn-danger {
            background: #f56565;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .log-container {
            max-height: 300px;
            overflow-y: auto;
            background: #f7fafc;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            line-height: 1.4;
        }

        .log-entry {
            margin-bottom: 8px;
            padding: 5px 8px;
            border-radius: 4px;
        }

        .log-info { background: rgba(66, 153, 225, 0.1); color: #2b6cb0; }
        .log-success { background: rgba(72, 187, 120, 0.1); color: #2f855a; }
        .log-warning { background: rgba(237, 137, 54, 0.1); color: #c05621; }
        .log-error { background: rgba(245, 101, 101, 0.1); color: #c53030; }

        .config-section {
            margin-bottom: 25px;
        }

        .config-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #e2e8f0;
        }

        .config-label {
            font-weight: 600;
            color: #4a5568;
        }

        .config-control {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .toggle-switch {
            position: relative;
            width: 50px;
            height: 24px;
            background: #cbd5e0;
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .toggle-switch.active {
            background: #48bb78;
        }

        .toggle-slider {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s ease;
        }

        .toggle-switch.active .toggle-slider {
            transform: translateX(26px);
        }

        .input-field {
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 0.9rem;
            width: 100px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #48bb78, #38a169);
            transition: width 0.3s ease;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid;
        }

        .alert-info {
            background: rgba(66, 153, 225, 0.1);
            border-color: #4299e1;
            color: #2b6cb0;
        }

        .alert-warning {
            background: rgba(237, 137, 54, 0.1);
            border-color: #ed8936;
            color: #c05621;
        }

        .alert-success {
            background: rgba(72, 187, 120, 0.1);
            border-color: #48bb78;
            color: #2f855a;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #e2e8f0;
            border-radius: 50%;
            border-top-color: #667eea;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .metric-grid {
                grid-template-columns: 1fr;
            }
            
            .dashboard-header h1 {
                font-size: 2rem;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- 头部 -->
        <div class="dashboard-header">
            <h1>智能学习型格式预处理引擎</h1>
            <p>管理面板 - 实时监控学习系统状态和性能</p>
        </div>

        <!-- 系统状态概览 -->
        <div class="dashboard-grid">
            <!-- 系统状态卡片 -->
            <div class="dashboard-card">
                <div class="card-header">
                    <div class="card-icon" style="background: linear-gradient(135deg, #48bb78, #38a169);">
                        📊
                    </div>
                    <div class="card-title">系统状态</div>
                </div>
                
                <div class="metric-grid">
                    <div class="metric-item">
                        <div class="metric-value" id="system-status">
                            <span class="status-indicator status-active"></span>运行中
                        </div>
                        <div class="metric-label">系统状态</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="uptime">--</div>
                        <div class="metric-label">运行时间</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="total-operations">0</div>
                        <div class="metric-label">总操作数</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="success-rate">--</div>
                        <div class="metric-label">成功率</div>
                    </div>
                </div>

                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="refreshSystemStatus()">
                        <span class="loading-spinner hidden" id="refresh-spinner"></span>
                        刷新状态
                    </button>
                    <button class="btn btn-secondary" onclick="exportSystemReport()">导出报告</button>
                </div>
            </div>

            <!-- 学习效果卡片 -->
            <div class="dashboard-card">
                <div class="card-header">
                    <div class="card-icon" style="background: linear-gradient(135deg, #667eea, #764ba2);">
                        🧠
                    </div>
                    <div class="card-title">学习效果</div>
                </div>
                
                <div class="metric-grid">
                    <div class="metric-item">
                        <div class="metric-value" id="learning-score">--</div>
                        <div class="metric-label">学习分数</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="accuracy-rate">--</div>
                        <div class="metric-label">准确率</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="rule-count">0</div>
                        <div class="metric-label">学习规则数</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="prediction-accuracy">--</div>
                        <div class="metric-label">预测准确率</div>
                    </div>
                </div>

                <div class="progress-bar">
                    <div class="progress-fill" id="learning-progress" style="width: 0%"></div>
                </div>
                <div style="text-align: center; margin-top: 10px; color: #718096; font-size: 0.9rem;">
                    学习进度
                </div>

                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="runLearningEvaluation()">运行评估</button>
                    <button class="btn btn-secondary" onclick="viewLearningHistory()">查看历史</button>
                </div>
            </div>

            <!-- 性能监控卡片 -->
            <div class="dashboard-card">
                <div class="card-header">
                    <div class="card-icon" style="background: linear-gradient(135deg, #ed8936, #dd6b20);">
                        ⚡
                    </div>
                    <div class="card-title">性能监控</div>
                </div>
                
                <div class="metric-grid">
                    <div class="metric-item">
                        <div class="metric-value" id="response-time">--</div>
                        <div class="metric-label">平均响应时间</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="memory-usage">--</div>
                        <div class="metric-label">内存使用</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="cache-hit-rate">--</div>
                        <div class="metric-label">缓存命中率</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="error-rate">--</div>
                        <div class="metric-label">错误率</div>
                    </div>
                </div>

                <div class="chart-container">
                    <canvas id="performance-chart"></canvas>
                </div>

                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="optimizePerformance()">性能优化</button>
                    <button class="btn btn-secondary" onclick="clearCache()">清理缓存</button>
                </div>
            </div>

            <!-- 缓存管理卡片 -->
            <div class="dashboard-card">
                <div class="card-header">
                    <div class="card-icon" style="background: linear-gradient(135deg, #9f7aea, #805ad5);">
                        💾
                    </div>
                    <div class="card-title">缓存管理</div>
                </div>
                
                <div class="metric-grid">
                    <div class="metric-item">
                        <div class="metric-value" id="cache-size">--</div>
                        <div class="metric-label">缓存大小</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="cache-hits">--</div>
                        <div class="metric-label">缓存命中</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="cache-misses">--</div>
                        <div class="metric-label">缓存未命中</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="preload-queue">--</div>
                        <div class="metric-label">预加载队列</div>
                    </div>
                </div>

                <div class="chart-container">
                    <canvas id="cache-chart"></canvas>
                </div>

                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="optimizeCache()">优化缓存</button>
                    <button class="btn btn-danger" onclick="clearAllCache()">清空缓存</button>
                </div>
            </div>
        </div>

        <!-- 详细信息区域 -->
        <div class="dashboard-grid">
            <!-- 系统配置卡片 -->
            <div class="dashboard-card">
                <div class="card-header">
                    <div class="card-icon" style="background: linear-gradient(135deg, #4299e1, #3182ce);">
                        ⚙️
                    </div>
                    <div class="card-title">系统配置</div>
                </div>

                <div class="config-section">
                    <div class="config-item">
                        <div class="config-label">启用学习系统</div>
                        <div class="config-control">
                            <div class="toggle-switch active" id="learning-enabled" onclick="toggleLearning()">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="config-item">
                        <div class="config-label">自动优化</div>
                        <div class="config-control">
                            <div class="toggle-switch active" id="auto-optimization" onclick="toggleAutoOptimization()">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="config-item">
                        <div class="config-label">性能监控</div>
                        <div class="config-control">
                            <div class="toggle-switch active" id="performance-monitoring" onclick="togglePerformanceMonitoring()">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="config-item">
                        <div class="config-label">缓存大小限制</div>
                        <div class="config-control">
                            <input type="number" class="input-field" id="cache-limit" value="1000" min="100" max="5000">
                            <span>项</span>
                        </div>
                    </div>
                    
                    <div class="config-item">
                        <div class="config-label">学习规则保留天数</div>
                        <div class="config-control">
                            <input type="number" class="input-field" id="retention-days" value="30" min="7" max="365">
                            <span>天</span>
                        </div>
                    </div>
                </div>

                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="saveConfiguration()">保存配置</button>
                    <button class="btn btn-secondary" onclick="resetConfiguration()">重置配置</button>
                </div>
            </div>

            <!-- 系统日志卡片 -->
            <div class="dashboard-card">
                <div class="card-header">
                    <div class="card-icon" style="background: linear-gradient(135deg, #38b2ac, #319795);">
                        📝
                    </div>
                    <div class="card-title">系统日志</div>
                </div>

                <div class="action-buttons" style="margin-bottom: 15px;">
                    <button class="btn btn-secondary" onclick="filterLogs('all')" id="filter-all">全部</button>
                    <button class="btn btn-secondary" onclick="filterLogs('info')" id="filter-info">信息</button>
                    <button class="btn btn-secondary" onclick="filterLogs('warning')" id="filter-warning">警告</button>
                    <button class="btn btn-secondary" onclick="filterLogs('error')" id="filter-error">错误</button>
                </div>

                <div class="log-container" id="log-container">
                    <!-- 日志条目将在这里动态加载 -->
                </div>

                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="refreshLogs()">刷新日志</button>
                    <button class="btn btn-secondary" onclick="exportLogs()">导出日志</button>
                    <button class="btn btn-danger" onclick="clearLogs()">清空日志</button>
                </div>
            </div>
        </div>

        <!-- 报警和通知区域 -->
        <div id="alerts-container">
            <!-- 动态生成的报警信息 -->
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="js/logger.js"></script>
    <script src="js/learning-engine/learning-config.js"></script>
    <script src="js/learning-engine/learning-storage-manager.js"></script>
    <script src="js/learning-engine/user-operation-learner.js"></script>
    <script src="js/learning-engine/error-classification-system.js"></script>
    <script src="js/learning-engine/pattern-matching-engine.js"></script>
    <script src="js/learning-engine/manual-correction-interface.js"></script>
    <script src="js/learning-engine/rule-generation-engine.js"></script>
    <script src="js/learning-engine/learning-integration-manager.js"></script>
    <script src="js/learning-engine/ui-correction-manager.js"></script>
    <script src="js/learning-engine/data-persistence-manager.js"></script>
    <script src="js/learning-engine/predictive-corrector.js"></script>
    <script src="js/learning-engine/adaptive-prompt-optimizer.js"></script>
    <script src="js/learning-engine/learning-effectiveness-evaluator.js"></script>
    <script src="js/learning-engine/intelligent-cache-manager.js"></script>
    <script src="js/learning-engine/performance-monitor.js"></script>
    <script src="js/learning-engine/performance-optimizer.js"></script>
    <script src="js/learning-engine/dashboard-manager.js"></script>
</body>
</html>
