<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多订单检测调试工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            padding: 20px;
        }
        
        .input-section {
            display: flex;
            flex-direction: column;
        }
        
        .output-section {
            display: flex;
            flex-direction: column;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #555;
            border-bottom: 2px solid #667eea;
            padding-bottom: 5px;
        }
        
        .text-input {
            width: 100%;
            height: 300px;
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
            resize: vertical;
            box-sizing: border-box;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
        
        .result-display {
            width: 100%;
            height: 400px;
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 11px;
            background: #f8f9fa;
            overflow-y: auto;
            white-space: pre-wrap;
            box-sizing: border-box;
        }
        
        .log-display {
            width: 100%;
            height: 300px;
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 11px;
            background: #000;
            color: #00ff00;
            overflow-y: auto;
            white-space: pre-wrap;
            box-sizing: border-box;
        }
        
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status.loading {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        
        .status.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .info-box {
            background: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }
        
        .stat-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 10px;
            text-align: center;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 多订单检测调试工具</h1>
            <p>用于诊断 GeminiService.detectAndSplitMultiOrders() 方法的功能</p>
        </div>
        
        <div class="main-content">
            <div class="input-section">
                <div class="section-title">📝 输入测试文本</div>
                
                <textarea id="orderTextInput" class="text-input" placeholder="在此输入要测试的订单文本...">[2025/7/15 15:42] Joshua: 送机：

团号：EJBTBY250717
2PAX
21/7 MOXY PUTRAJAYA PICKUP 0600 - KLIA2 (AK188 1000)
客人：简锦霞
客人联系： ***********
[2025/7/15 18:48] Joshua: 接机：

团号：EJBTBY250716-7
2PAX
16/7 KLIA IN 1840 (MH377) - KOMUNE LIVING PERMAISURI
客人：周有改
客人联系：***********
[2025/7/15 18:48] Joshua: 7 SEATER

接机：

团号：EJBTBY250717-1
4PAX
17/7 KLIA IN 1325 - DAYS HOTEL FRASER BUSINESS PARK
客人：谭建玲
客人联系： ***********
[2025/7/15 18:48] Joshua: 7 SEATER

送机：

团号：EJBTBY250717-1
4PAX
21/7 MOXY PUTRAJAYA PICKUP 0400 - KLIA2 (AK5106 0800)
客人：谭建玲
客人联系： ***********
[2025/7/15 21:53] Joshua: 送机：

团号：EJBTBY250712-1
2PAX
16/7 CONCORDE SHAH ALAM PICKUP 0530 - KLIA2 (AK707 0935)
客人：朱芸 
客人联系：***********
[2025/7/15 23:00] Joshua: 酒店接送：

团号：EJBTBY250714-1
2PAX
16/7 Crown Regency Serviced Suites PICKUP 1200 - Hotel Komune Living & Wellness (PERMAISURI)
客人：吴敏 
客人联系：***********</textarea>
                
                <div class="button-group">
                    <button class="btn btn-primary" onclick="startDetection()">🚀 开始检测</button>
                    <button class="btn btn-secondary" onclick="clearLogs()">🧹 清空日志</button>
                    <button class="btn btn-warning" onclick="resetAll()">🔄 重置所有</button>
                    <button class="btn btn-success" onclick="testEvent()">📡 测试事件</button>
                </div>
                
                <div id="statusDisplay" class="status" style="display: none;"></div>
                
                <div class="stats">
                    <div class="stat-item">
                        <div class="stat-value" id="charCount">0</div>
                        <div class="stat-label">字符数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="orderCount">-</div>
                        <div class="stat-label">检测订单数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="confidence">-</div>
                        <div class="stat-label">置信度</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="responseTime">-</div>
                        <div class="stat-label">响应时间(ms)</div>
                    </div>
                </div>
                
                <div class="info-box">
                    <strong>💡 调试说明：</strong><br>
                    • 此工具直接调用 GeminiService.detectAndSplitMultiOrders() 方法<br>
                    • 检查 AI 解析结果的完整性和格式正确性<br>
                    • 验证多订单事件的触发机制<br>
                    • 预填入的文本包含6个不同的订单，应被识别为多订单
                </div>
            </div>
            
            <div class="output-section">
                <div class="section-title">📊 检测结果 (JSON)</div>
                <div id="resultDisplay" class="result-display">等待检测结果...</div>
                
                <div class="section-title" style="margin-top: 20px;">📋 执行日志</div>
                <div id="logDisplay" class="log-display">调试日志将在此显示...</div>
            </div>
        </div>
    </div>

    <!-- 依赖脚本 -->
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/monitoring-wrapper.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/gemini-service.js"></script>
    
    <!-- 调试脚本 -->
    <script src="js/debug-multi-order.js"></script>
</body>
</html>